/* Feature Sections - Tighter Spacing */
.map-feature-section {
    margin-bottom: var(--map-space-4);
}

.map-feature-section:last-child {
    margin-bottom: 0;
}

/* Add extra spacing after Line Spacing section */
#map-line-spacing-toggle {
    /* Line spacing toggle specific styles if needed */
}

.map-feature-section:has(#map-line-spacing-toggle),
.map-feature-section:nth-last-child(1) {
    margin-bottom: calc(var(--map-space-6) + var(--map-space-4));
}

.map-feature-title {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--map-text-color);
}

/* Feature Toggle Cards - Modern Design */
.map-feature-toggle {
    display: flex;
    align-items: center;
    width: 100%;
    background: var(--map-white);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-5);
    cursor: pointer;
    transition: all var(--map-transition-base);
    color: var(--map-gray-700);
    text-align: left;
    margin-bottom: var(--map-space-3);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.map-feature-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    opacity: 0;
    transition: opacity var(--map-transition-base);
    pointer-events: none;
}

/* Premium Feature Toggle Hover - Matched with Category Dramatic Style */
.map-feature-toggle:hover {
    background: linear-gradient(135deg, var(--map-gray-50) 0%, var(--map-white) 100%);
    border-color: var(--map-primary);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.15), 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Premium Feature Icon Hover Effects - Matched with Category Dramatic Style */
.map-feature-toggle:hover .map-feature-icon {
    transform: scale(1.08) translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
}

/* Premium Active state for feature toggle - Light Grey with Blue Accents */
.map-feature-toggle.active,
.map-feature-toggle[data-active="true"] {
    background: linear-gradient(135deg, var(--map-gray-50) 0%, var(--map-gray-100) 100%);
    border: 2px solid var(--map-primary);
    color: var(--map-gray-800);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), var(--map-shadow-lg);
    transform: translateY(-2px);
}

.map-feature-toggle.active::before,
.map-feature-toggle[data-active="true"]::before {
    opacity: 0.1;
}

.map-feature-toggle.active .map-feature-title,
.map-feature-toggle[data-active="true"] .map-feature-title {
    color: var(--map-gray-900);
    font-weight: 600;
}

.map-feature-toggle.active .map-feature-desc,
.map-feature-toggle[data-active="true"] .map-feature-desc {
    color: var(--map-gray-600);
}

/* Premium Feature Icons - Unified with Category Style */
.map-feature-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-radius: var(--map-radius-lg);
    margin-right: var(--map-space-4);
    flex-shrink: 0;
    transition: all var(--map-transition-base);
    position: relative;
    z-index: 1;
    pointer-events: none;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
}

.map-feature-icon svg {
    width: 24px;
    height: 24px;
    color: var(--map-white);
    stroke: currentColor;
    fill: currentColor;
    transition: color var(--map-transition-base);
}

.map-feature-toggle.active .map-feature-icon,
.map-feature-toggle[data-active="true"] .map-feature-icon {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.map-feature-toggle.active .map-feature-icon svg,
.map-feature-toggle[data-active="true"] .map-feature-icon svg {
    color: var(--map-white);
}

.map-feature-content {
    flex: 1;
    position: relative;
    z-index: 1;
    pointer-events: none;
}

.map-feature-toggle .map-feature-title {
    margin: 0 0 var(--map-space-1) 0;
    font-size: var(--map-font-size-base);
    font-weight: 600;
    color: var(--map-gray-800);
    line-height: 1.4;
    transition: color var(--map-transition-base);
}

.map-feature-desc {
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-500);
    margin: 0;
    line-height: 1.5;
    transition: color var(--map-transition-base);
}

/* Premium Feature Text Hover Effects */
.map-feature-toggle:hover .map-feature-title {
    color: var(--map-gray-900);
}

.map-feature-toggle:hover .map-feature-desc {
    color: var(--map-gray-600);
}

/* Premium Active Feature Toggle Hover Effects */
.map-feature-toggle.active:hover,
.map-feature-toggle[data-active="true"]:hover {
    background: linear-gradient(135deg, var(--map-gray-100) 0%, var(--map-white) 100%);
    border-color: var(--map-primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(99, 102, 241, 0.2), 0 5px 8px rgba(0, 0, 0, 0.08);
}

.map-feature-toggle.active:hover .map-feature-icon,
.map-feature-toggle[data-active="true"]:hover .map-feature-icon {
    transform: scale(1.08) translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
}

/* Premium Toggle Switch - Enhanced Design */
.map-toggle-switch {
    position: relative;
    width: 48px;
    height: 28px;
    background: linear-gradient(135deg, var(--map-gray-200) 0%, var(--map-gray-300) 100%);
    border-radius: var(--map-radius-full);
    border: 1px solid var(--map-gray-300);
    flex-shrink: 0;
    transition: all var(--map-transition-base);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.map-toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, var(--map-white) 0%, var(--map-gray-50) 100%);
    border: 1px solid var(--map-gray-200);
    border-radius: 50%;
    transition: all var(--map-transition-base);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
}

/* Premium Purple Toggle Switch for Active State */
.map-feature-toggle[data-active="true"] .map-toggle-switch,
.map-feature-toggle.active .map-toggle-switch {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%) !important;
    border-color: var(--map-primary) !important;
    box-shadow: 0 2px 8px rgba(124, 58, 237, 0.4) !important;
}

.map-feature-toggle[data-active="true"] .map-toggle-slider,
.map-feature-toggle.active .map-toggle-slider {
    transform: translateX(20px) !important;
    background: var(--map-white) !important;
    border-color: var(--map-white) !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Premium Toggle Switch Hover Effects - Enhanced Dramatic Style */
.map-feature-toggle:hover .map-toggle-switch {
    background: linear-gradient(135deg, var(--map-gray-300) 0%, var(--map-gray-400) 100%);
    border-color: var(--map-primary);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(99, 102, 241, 0.25);
    transform: scale(1.02);
}

.map-feature-toggle:hover .map-toggle-slider {
    background: linear-gradient(135deg, var(--map-white) 0%, var(--map-gray-50) 100%);
    border-color: var(--map-primary);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2), 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
}

/* Premium Active Toggle Switch Hover Effects - Purple Theme */
.map-feature-toggle.active:hover .map-toggle-switch,
.map-feature-toggle[data-active="true"]:hover .map-toggle-switch {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%) !important;
    border-color: var(--map-primary-dark) !important;
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.5),
                0 0 25px rgba(124, 58, 237, 0.3) !important;
    transform: scale(1.02) !important;
}

.map-feature-toggle.active:hover .map-toggle-slider,
.map-feature-toggle[data-active="true"]:hover .map-toggle-slider {
    background: var(--map-white) !important;
    border-color: var(--map-white) !important;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25), 0 2px 5px rgba(0, 0, 0, 0.15) !important;
    transform: translateX(20px) scale(1.05) !important;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Feature Status Display */
.map-feature-status {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 8px;
    background: rgba(var(--map-primary-rgb), 0.1);
    border: 1px solid rgba(var(--map-primary-rgb), 0.2);
    border-radius: var(--map-radius-md);
    font-size: var(--map-font-size-xs);
    font-weight: 500;
    color: var(--map-primary);
    min-width: 60px;
    position: relative;
    z-index: 1;
    pointer-events: none;
}

.map-feature-toggle.active .map-feature-status,
.map-feature-toggle[data-active="true"] .map-feature-status {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-color: var(--map-primary);
    color: var(--map-white);
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
}

/* Compact Feature Controls Container */
.map-feature-controls {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.9) 100%);
    border-top: 1px solid var(--map-gray-200);
    border-radius: 0 0 var(--map-radius-lg) var(--map-radius-lg);
    padding: var(--map-space-4);
    margin-top: 0;
    transition: all var(--map-transition-base);
    backdrop-filter: blur(10px);
}

.map-feature-button[aria-pressed="true"] {
    background: var(--map-primary-active);
}

/* Progress Bar */
.map-progress-container {
    margin-bottom: 16px;
}

.map-progress-bar {
    width: 100%;
    height: 6px;
    background: #e0e0e0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 4px;
}

.map-progress-fill {
    height: 100%;
    background: var(--map-primary-color);
    transition: width 0.3s ease;
}

.map-progress-text {
    font-size: 12px;
    color: #666;
}

/* Control Groups */
.map-control-group {
    margin-bottom: 16px;
}

.map-control-label {
    display: block;
    margin-bottom: 6px;
    font-size: 13px;
    font-weight: 500;
    color: #23282d;
}

/* Speed Control */
.map-speed-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.map-range-input {
    flex: 1;
    height: 6px;
    background: #e0e0e0;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.map-range-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: var(--map-primary-color);
    border-radius: 50%;
    cursor: pointer;
}

.map-range-input::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: var(--map-primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.map-speed-value {
    font-size: 12px;
    font-weight: 600;
    color: var(--map-primary-color);
    min-width: 30px;
    text-align: center;
}

/* Select Input */
.map-select-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--map-border-color);
    border-radius: 4px;
    background: var(--map-bg-color);
    font-size: 13px;
}

/* Option Groups */
.map-option-group {
    margin-bottom: 12px;
}

.map-checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 13px;
}

.map-checkbox-input {
    margin: 0;
}

/* Premium Dark Mode Feature Toggles - Enhanced Design */
.map-accessibility-widget.map-dark-mode .map-feature-toggle {
    background: linear-gradient(135deg, var(--map-surface-elevated) 0%, var(--map-gray-200) 100%);
    border: 1px solid var(--map-border-light);
    box-shadow: var(--map-shadow-sm),
                inset 0 1px 0 rgba(255, 255, 255, 0.03);
    transition: all var(--map-transition-base);
}

.map-accessibility-widget.map-dark-mode .map-feature-toggle:hover {
    background: linear-gradient(135deg, var(--map-surface-hover) 0%, var(--map-gray-300) 100%);
    border-color: var(--map-primary);
    box-shadow: var(--map-shadow-md),
                var(--map-glow-primary),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
    transform: translateY(-1px);
}

.map-accessibility-widget.map-dark-mode .map-feature-toggle.active,
.map-accessibility-widget.map-dark-mode .map-feature-toggle[data-active="true"] {
    background: linear-gradient(135deg, var(--map-gray-300) 0%, var(--map-gray-400) 100%);
    border: 2px solid var(--map-primary);
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2),
                var(--map-shadow-lg),
                inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.map-accessibility-widget.map-dark-mode .map-feature-toggle:focus-visible {
    outline: none;
    border-color: var(--map-primary);
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.3),
                var(--map-shadow-md);
}

/* Premium Dark Mode Text Hierarchy - WCAG AAA Compliant */
.map-accessibility-widget.map-dark-mode .map-feature-title,
.map-accessibility-widget.map-dark-mode .map-category-title,
.map-accessibility-widget.map-dark-mode .map-view-title {
    color: var(--map-text);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    letter-spacing: -0.01em;
}

.map-accessibility-widget.map-dark-mode .map-feature-desc,
.map-accessibility-widget.map-dark-mode .map-category-desc {
    color: var(--map-text-secondary);
    font-weight: 400;
    line-height: 1.5;
    opacity: 0.9;
}

/* Enhanced Text Contrast for Better Readability */
.map-accessibility-widget.map-dark-mode .map-panel-title {
    color: var(--map-text);
    font-weight: 700;
    font-size: 1.125rem;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.map-accessibility-widget.map-dark-mode .map-section-title {
    color: var(--map-text);
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.75rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.map-accessibility-widget.map-dark-mode .map-help-text,
.map-accessibility-widget.map-dark-mode .map-description-text {
    color: var(--map-text-muted);
    font-size: 0.875rem;
    line-height: 1.6;
    opacity: 0.85;
}

/* Dark Mode Link Colors - High Contrast */
.map-accessibility-widget.map-dark-mode a {
    color: var(--map-primary-light);
    text-decoration: none;
    transition: color var(--map-transition-fast);
}

.map-accessibility-widget.map-dark-mode a:hover {
    color: var(--map-accent-light);
    text-shadow: 0 0 8px rgba(251, 191, 36, 0.3);
}

.map-accessibility-widget.map-dark-mode a:focus-visible {
    outline: 2px solid var(--map-primary);
    outline-offset: 2px;
    border-radius: 2px;
}

/* Premium Dark Mode Buttons - Enhanced Interactive Effects */
.map-accessibility-widget.map-dark-mode .map-button,
.map-accessibility-widget.map-dark-mode button {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border: 1px solid var(--map-primary);
    color: var(--map-white);
    box-shadow: var(--map-shadow-sm),
                0 0 0 0 rgba(124, 58, 237, 0);
    transition: all var(--map-transition-base);
    position: relative;
    overflow: hidden;
}

.map-accessibility-widget.map-dark-mode .map-button:hover,
.map-accessibility-widget.map-dark-mode button:hover {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    border-color: var(--map-primary-light);
    box-shadow: var(--map-shadow-lg),
                var(--map-glow-primary),
                0 0 0 3px rgba(124, 58, 237, 0.2);
    transform: translateY(-2px);
}

.map-accessibility-widget.map-dark-mode .map-button:active,
.map-accessibility-widget.map-dark-mode button:active {
    transform: translateY(0);
    box-shadow: var(--map-shadow-sm),
                0 0 0 2px rgba(124, 58, 237, 0.3);
}

.map-accessibility-widget.map-dark-mode .map-button:focus-visible,
.map-accessibility-widget.map-dark-mode button:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.4),
                var(--map-shadow-md);
}

/* Dark Mode Secondary Buttons */
.map-accessibility-widget.map-dark-mode .map-button-secondary {
    background: linear-gradient(135deg, var(--map-surface-elevated) 0%, var(--map-gray-200) 100%);
    border: 1px solid var(--map-border);
    color: var(--map-text);
}

.map-accessibility-widget.map-dark-mode .map-button-secondary:hover {
    background: linear-gradient(135deg, var(--map-surface-hover) 0%, var(--map-gray-300) 100%);
    border-color: var(--map-primary);
    color: var(--map-primary-light);
    box-shadow: var(--map-shadow-md),
                var(--map-glow-primary);
}

/* Premium Dark Mode Form Controls - Enhanced Design */
.map-accessibility-widget.map-dark-mode input[type="range"],
.map-accessibility-widget.map-dark-mode .map-slider {
    background: var(--map-gray-300);
    border-radius: 4px;
}

.map-accessibility-widget.map-dark-mode input[type="range"]::-webkit-slider-thumb {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border: 2px solid var(--map-white);
    box-shadow: var(--map-shadow-md),
                0 0 8px rgba(124, 58, 237, 0.3);
}

.map-accessibility-widget.map-dark-mode input[type="range"]::-webkit-slider-thumb:hover {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    box-shadow: var(--map-shadow-lg),
                var(--map-glow-primary);
    transform: scale(1.1);
}

.map-accessibility-widget.map-dark-mode input[type="color"] {
    border: 2px solid var(--map-border);
    border-radius: var(--map-radius-md);
    box-shadow: var(--map-shadow-sm);
    transition: all var(--map-transition-base);
}

.map-accessibility-widget.map-dark-mode input[type="color"]:hover {
    border-color: var(--map-primary);
    box-shadow: var(--map-shadow-md),
                var(--map-glow-primary);
}

.map-accessibility-widget.map-dark-mode input[type="color"]:focus-visible {
    outline: none;
    border-color: var(--map-primary);
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.3),
                var(--map-shadow-md);
}

/* Dark Mode Toggle Switches - Enhanced Purple Glow Effects */
.map-accessibility-widget.map-dark-mode .map-toggle-switch {
    background: var(--map-gray-400);
    border: 1px solid var(--map-gray-300);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Dark Mode Active Toggle - Stay Purple */
.map-accessibility-widget.map-dark-mode .map-feature-toggle[data-active="true"] .map-toggle-switch,
.map-accessibility-widget.map-dark-mode .map-feature-toggle.active .map-toggle-switch,
.map-accessibility-widget.map-dark-mode .map-toggle-switch.active {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%) !important;
    border-color: var(--map-primary) !important;
    box-shadow: 0 0 20px rgba(124, 58, 237, 0.4),
                0 2px 8px rgba(124, 58, 237, 0.3),
                inset 0 1px 2px rgba(255, 255, 255, 0.1) !important;
}

.map-accessibility-widget.map-dark-mode .map-toggle-slider {
    background: linear-gradient(135deg, var(--map-white) 0%, var(--map-gray-100) 100%);
    box-shadow: var(--map-shadow-sm);
}

/* Dark Mode Active Toggle Slider - Stay Moved */
.map-accessibility-widget.map-dark-mode .map-feature-toggle[data-active="true"] .map-toggle-slider,
.map-accessibility-widget.map-dark-mode .map-feature-toggle.active .map-toggle-slider,
.map-accessibility-widget.map-dark-mode .map-toggle-switch.active .map-toggle-slider {
    background: var(--map-white) !important;
    transform: translateX(20px) !important;
    box-shadow: var(--map-shadow-md),
                0 0 8px rgba(255, 255, 255, 0.3) !important;
}

/* Premium Dark Mode Close Button - Enhanced Glow */
.map-accessibility-widget.map-dark-mode .map-close-button {
    background: linear-gradient(135deg, var(--map-surface-elevated) 0%, var(--map-gray-200) 100%);
    border: 1px solid var(--map-border);
    color: var(--map-text-secondary);
    box-shadow: var(--map-shadow-sm);
    transition: all var(--map-transition-base);
}

.map-accessibility-widget.map-dark-mode .map-close-button:hover {
    background: linear-gradient(135deg, var(--map-error) 0%, #f87171 100%);
    border-color: var(--map-error);
    color: var(--map-white);
    box-shadow: var(--map-shadow-lg),
                0 0 20px rgba(239, 68, 68, 0.4);
    transform: translateY(-1px) scale(1.05);
}

.map-accessibility-widget.map-dark-mode .map-close-button:focus-visible {
    outline: none;
    border-color: var(--map-error);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3),
                var(--map-shadow-md);
}

/* Dark Mode Back Button - Enhanced Design */
.map-accessibility-widget.map-dark-mode .map-back-button,
.map-accessibility-widget.map-dark-mode .map-header-back-button {
    background: linear-gradient(135deg, var(--map-surface-elevated) 0%, var(--map-gray-200) 100%);
    border: 1px solid var(--map-border);
    color: var(--map-text);
    box-shadow: var(--map-shadow-sm);
}

.map-accessibility-widget.map-dark-mode .map-back-button:hover,
.map-accessibility-widget.map-dark-mode .map-header-back-button:hover {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-color: var(--map-primary);
    color: var(--map-white);
    box-shadow: var(--map-shadow-lg),
                var(--map-glow-primary);
    transform: translateY(-2px) translateX(-2px);
}

/* Dark Mode Reset Button - Enhanced Styling */
.map-accessibility-widget.map-dark-mode .map-reset-button {
    background: linear-gradient(135deg, var(--map-surface-elevated) 0%, var(--map-gray-200) 100%);
    border: 1px solid var(--map-border);
    color: var(--map-text-secondary);
    box-shadow: var(--map-shadow-sm);
}

.map-accessibility-widget.map-dark-mode .map-reset-button:hover {
    background: linear-gradient(135deg, var(--map-accent) 0%, var(--map-accent-light) 100%);
    border-color: var(--map-accent);
    color: var(--map-gray-900);
    box-shadow: var(--map-shadow-lg),
                var(--map-glow-accent);
    transform: translateY(-1px) scale(1.05);
}
