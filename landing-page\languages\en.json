{"main_titles": {"Accessibility Tools": "Accessibility Tools", "Preferences": "Preferences", "Text Options": "Text Options", "Navigation Options": "Navigation Options", "Contrast & Colors": "Contrast & Colors"}, "feature_titles": {"Text-to-Speech": "Text-to-Speech", "Dyslexic Font": "Dyslexic Font", "Reading Guide": "Reading Guide", "Font Size": "Font Size", "Line Spacing": "Line Spacing", "ADHD Focus Mode": "ADHD Focus Mode", "Big Cursor": "Big Cursor", "Text Magnification": "Text Magnification", "Visual Themes": "Visual Themes", "Color Studio": "Color Studio", "Dark mode": "Dark mode", "Language": "Language", "Menu Position": "Menu Position"}, "feature_descriptions": {"Listen to any text on the website with natural voice synthesis": "Listen to any text on the website with natural voice synthesis", "Switch to a font designed for better readability": "Switch to a font designed for better readability", "Follow a reading line that moves with your cursor": "Follow a reading line that moves with your cursor", "Adjust text size for better readability": "Adjust text size for better readability", "Increase space between lines for easier reading": "Increase space between lines for easier reading", "Highlight content and reduce distractions": "Highlight content and reduce distractions", "Increase cursor size for better visibility": "Increase cursor size for better visibility", "Magnify text when hovering over it": "Magnify text when hovering over it", "Choose from high contrast themes for better visibility": "Choose from high contrast themes for better visibility", "Design your perfect color palette with live preview": "Design your perfect color palette with live preview", "Switch to a dark theme for the accessibility menu interface": "Switch to a dark theme for the accessibility menu interface", "Choose your preferred interface language": "Choose your preferred interface language", "Choose where the accessibility menu button appears on screen": "Choose where the accessibility menu button appears on screen", "Select text to hear it read aloud": "Select text to hear it read aloud", "Apply dyslexia-friendly font to improve readability": "Apply dyslexia-friendly font to improve readability", "Show a horizontal line that follows your mouse to help focus on text": "Show a horizontal line that follows your mouse to help focus on text", "Reduce distractions and highlight content for better focus": "Reduce distractions and highlight content for better focus", "Enlarge cursor size for better visibility and easier tracking": "Enlarge cursor size for better visibility and easier tracking", "Magnify text on hover for better readability and visibility": "Magnify text on hover for better readability and visibility", "Choose a visual style that works best for you": "Choose a visual style that works best for you"}, "language_options": {"English": "English", "French": "French"}, "category_descriptions": {"Reading and text options": "Reading and text options", "Contrast and color themes": "Contrast and color themes", "Keyboard shortcuts and navigation aids": "Keyboard shortcuts and navigation aids", "General settings and preferences": "General settings and preferences"}, "navigation": {"Back to main menu": "Back to main menu", "Text": "Text", "Navigation": "Navigation"}, "status_messages": {"Text-to-Speech is now active. Select any text to hear it read aloud.": "Text-to-Speech is now active. Select any text to hear it read aloud.", "Dyslexic font applied! Text should now be easier to read.": "Dyslexic font applied! Text should now be easier to read.", "Reading guide is now active. Move your cursor to see the guide line.": "Reading guide is now active. Move your cursor to see the guide line.", "Font size has been adjusted. The change applies to all text on the website.": "Font size has been adjusted. The change applies to all text on the website.", "Line spacing has been adjusted. The change applies to all text on the website.": "Line spacing has been adjusted. The change applies to all text on the website.", "ADHD Focus Mode is active. Content is highlighted and distractions are minimized.": "ADHD Focus Mode is active. Content is highlighted and distractions are minimized.", "Big cursor mode is active. The cursor is now larger and easier to see.": "Big cursor mode is active. The cursor is now larger and easier to see.", "Text magnification is active. Hover over text to see it magnified.": "Text magnification is active. Hover over text to see it magnified.", "Dark interface theme applied!": "Dark interface theme applied!", "Light interface theme restored": "Light interface theme restored", "Language preference updated!": "Language preference updated!", "Language changed to English!": "Language changed to English!", "Language changed to Français!": "Language changed to Français!", "Reset to Default": "Reset to De<PERSON>ult", "Big Cursor is active. Cursor size has been enlarged for better visibility.": "Big Cursor is active. Cursor size has been enlarged for better visibility.", "Theme applied successfully. The visual appearance of the website has been updated.": "Theme applied successfully. The visual appearance of the website has been updated.", "Custom theme applied successfully!": "Custom theme applied successfully!"}, "controls_buttons": {"Speed": "Speed", "Reset": "Reset", "Text": "Text", "Background": "Background", "Links": "Links", "Headings": "Headings", "Body text & paragraphs": "Body text & paragraphs", "Page & content areas": "Page & content areas", "Hyperlinks & buttons": "Hyperlinks & buttons", "Titles & headings": "Titles & headings"}, "theme_names": {"Default": "<PERSON><PERSON><PERSON>", "High Contrast": "High Contrast", "Dark Theme": "Dark Theme", "Yellow & Black": "Yellow & Black", "Blue & White": "Blue & White"}, "theme_descriptions": {"Standard website appearance with default colors": "Standard website appearance with default colors", "Black text on white background for maximum readability": "Black text on white background for maximum readability", "White text on dark background to reduce eye strain": "White text on dark background to reduce eye strain", "High contrast yellow background with black text": "High contrast yellow background with black text", "Calming blue background with white text": "Calming blue background with white text"}, "color_descriptions": {"Titles & headers": "Titles & headers", "Body text & paragraphs": "Body text & paragraphs", "Page & content areas": "Page & content areas", "Hyperlinks & buttons": "Hyperlinks & buttons"}, "language_native_names": {"English": "English", "Français": "Français"}, "line_spacing_labels": {"Tight": "Tight", "Normal": "Normal", "Wide": "Wide", "Default": "<PERSON><PERSON><PERSON>", "TIGHT": "TIGHT", "NORMAL": "NORMAL", "WIDE": "WIDE", "DEFAULT": "DEFAULT", "RESET TO DEFAULT": "RESET TO DEFAULT"}, "menu_position": {"Top Left": "Top Left", "Top Right": "Top Right", "Bottom Left": "Bottom Left", "Bottom Right": "Bottom Right", "Menu moved to top left!": "<PERSON><PERSON> moved to top left!", "Menu moved to top right!": "<PERSON><PERSON> moved to top right!", "Menu moved to bottom left!": "<PERSON><PERSON> moved to bottom left!", "Menu moved to bottom right!": "<PERSON><PERSON> moved to bottom right!", "Menu position updated!": "Menu position updated!"}}