/* ===== LANGUAGE SELECTION STYLES ===== */

/* Language Options Container */
.map-language-options {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-2);
    margin-bottom: var(--map-space-4);
}

/* Individual Language Option */
.map-language-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--map-space-3) var(--map-space-4);
    background: var(--map-white);
    border: 2px solid var(--map-gray-200);
    border-radius: var(--map-radius-lg);
    cursor: pointer;
    transition: all var(--map-transition-base);
    position: relative;
    overflow: hidden;
}

.map-language-option:hover {
    border-color: var(--map-primary);
    background: rgba(124, 58, 237, 0.02);
    transform: translateY(-1px);
    box-shadow:
        0 4px 12px rgba(124, 58, 237, 0.1),
        0 2px 4px rgba(0, 0, 0, 0.05);
}

.map-language-option.active {
    border-color: var(--map-primary);
    background: linear-gradient(135deg, rgba(124, 58, 237, 0.12) 0%, rgba(124, 58, 237, 0.06) 100%);
    box-shadow:
        0 0 0 2px rgba(124, 58, 237, 0.3),
        0 4px 12px rgba(124, 58, 237, 0.2),
        0 2px 4px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.map-language-option.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--map-primary);
    border-radius: 0 2px 2px 0;
}

/* Language Info Section */
.map-language-info {
    display: flex;
    align-items: center;
    gap: var(--map-space-3);
}

.map-language-flag {
    font-size: 24px;
    line-height: 1;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.map-language-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.map-language-name {
    font-size: 15px;
    font-weight: 600;
    color: var(--map-text);
    line-height: 1.2;
}

.map-language-native {
    font-size: 13px;
    color: var(--map-text-muted);
    font-weight: 400;
    line-height: 1.2;
}

/* Language Status/Check */
.map-language-status {
    display: flex;
    align-items: center;
    justify-content: center;
}

.map-language-check {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--map-gray-100);
    border: 2px solid var(--map-gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--map-transition-base);
    opacity: 0;
}

.map-language-option.active .map-language-check {
    background: var(--map-primary);
    border-color: var(--map-primary);
    opacity: 1;
    transform: scale(1.1);
}

.map-language-option.active .map-language-check svg {
    color: white;
    stroke-width: 2.5;
}



/* ===== MENU POSITION GRID LAYOUT ===== */

/* Position Grid - 2x2 Layout for menu position options */
.map-position-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--map-space-3);
    margin-bottom: var(--map-space-4);
}

/* Compact Control Header */
.map-control-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--map-space-3);
    padding: var(--map-space-2) 0;
}

.map-control-title {
    font-size: var(--map-font-size-sm);
    font-weight: 600;
    color: var(--map-gray-700);
    margin: 0;
    letter-spacing: -0.025em;
}

/* Hide Default indicators to save space and clean up interface */
.map-control-value {
    display: none;
}

/* TTS Controls */
.map-tts-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

/* Floating Play Button */
.map-floating-play-button {
    position: absolute;
    z-index: var(--map-z-index);
    animation: mapFadeInUp 0.3s ease-out;
}

.map-play-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--map-primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--map-shadow);
    transition: var(--map-transition);
    position: relative;
}

.map-play-btn:hover {
    background: var(--map-primary-hover);
    transform: scale(1.1);
    box-shadow: var(--map-shadow-hover);
}

.map-play-btn:active {
    background: var(--map-primary-active);
    transform: scale(0.95);
}

.map-play-btn svg {
    margin-left: 2px; /* Slight offset to center the play icon visually */
}

/* Stop button state */
.map-play-btn.map-stop-btn {
    background: #dc2626; /* Red background for stop */
}

.map-play-btn.map-stop-btn:hover {
    background: #b91c1c; /* Darker red on hover */
}

.map-play-btn.map-stop-btn:active {
    background: #991b1b; /* Even darker red when active */
}

.map-play-btn.map-stop-btn svg {
    margin-left: 0; /* No offset needed for square stop icon */
}

/* Loading state */
.map-play-btn.map-loading {
    background: #6b7280; /* Gray background during loading */
    cursor: wait;
}

.map-play-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.map-loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: mapSpin 1s ease-in-out infinite;
}

/* Loading spinner animation */
@keyframes mapSpin {
    to {
        transform: rotate(360deg);
    }
}

/* Floating button animation */
@keyframes mapFadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.map-feature-button {
    display: flex;
    align-items: center;
    gap: 6px;
    background: var(--map-primary-color);
    color: var(--map-text-color);
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: var(--map-transition);
    flex: 1;
    justify-content: center;
}

.map-feature-button:hover:not(:disabled) {
    background: var(--map-primary-hover);
}

.map-feature-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    opacity: 0.6;
}

/* ===== LANGUAGE SELECTION DARK MODE ===== */

.map-accessibility-widget.map-dark-mode .map-language-option {
    background: var(--map-gray-200);
    border-color: var(--map-border);
}

.map-accessibility-widget.map-dark-mode .map-language-option:hover {
    border-color: var(--map-primary);
    background: rgba(124, 58, 237, 0.1);
}

.map-accessibility-widget.map-dark-mode .map-language-option.active {
    border-color: var(--map-primary);
    background: linear-gradient(135deg, rgba(124, 58, 237, 0.18) 0%, rgba(124, 58, 237, 0.10) 100%);
    box-shadow:
        0 0 0 2px rgba(124, 58, 237, 0.4),
        0 4px 12px rgba(124, 58, 237, 0.25),
        0 2px 4px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.map-accessibility-widget.map-dark-mode .map-language-name {
    color: var(--map-white) !important;
}

.map-accessibility-widget.map-dark-mode .map-language-native {
    color: var(--map-text-muted) !important;
}

/* Ensure all language text is white in dark mode */
.map-accessibility-widget.map-dark-mode .map-language-option .map-language-name,
.map-accessibility-widget.map-dark-mode .map-language-option.active .map-language-name,
.map-accessibility-widget.map-dark-mode .map-language-option .map-language-details .map-language-name,
.map-accessibility-widget.map-dark-mode .map-language-option.active .map-language-details .map-language-name {
    color: white !important;
}

/* Ensure language details container text is white */
.map-accessibility-widget.map-dark-mode .map-language-details {
    color: white !important;
}

.map-accessibility-widget.map-dark-mode .map-language-details * {
    color: inherit !important;
}

.map-accessibility-widget.map-dark-mode .map-language-check {
    background: var(--map-gray-300);
    border-color: var(--map-border);
}

.map-accessibility-widget.map-dark-mode .map-language-option.active .map-language-check {
    background: var(--map-primary);
    border-color: var(--map-primary);
}


