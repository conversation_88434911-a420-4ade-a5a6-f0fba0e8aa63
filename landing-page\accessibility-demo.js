/**
 * Simple Accessibility Widget Demo
 * This is a simplified version for the landing page demo
 */

class AccessibilityDemo {
    constructor() {
        this.isOpen = false;
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.bindEvents());
        } else {
            this.bindEvents();
        }
    }

    bindEvents() {
        // Main toggle button
        const toggleButton = document.getElementById('map-main-toggle');
        const closeButton = document.getElementById('map-close-panel');
        
        if (toggleButton) {
            toggleButton.addEventListener('click', () => this.togglePanel());
        }
        
        if (closeButton) {
            closeButton.addEventListener('click', () => this.closePanel());
        }

        // Close panel when clicking outside
        document.addEventListener('click', (e) => {
            const widget = document.getElementById('map-accessibility-widget');
            if (widget && this.isOpen && !widget.contains(e.target)) {
                this.closePanel();
            }
        });

        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closePanel();
            }
        });
    }

    togglePanel() {
        if (this.isOpen) {
            this.closePanel();
        } else {
            this.openPanel();
        }
    }

    openPanel() {
        const panel = document.getElementById('map-widget-panel');
        const toggleButton = document.getElementById('map-main-toggle');
        
        if (panel && toggleButton) {
            panel.style.display = 'block';
            panel.setAttribute('aria-hidden', 'false');
            toggleButton.setAttribute('aria-expanded', 'true');
            
            // Add animation class
            setTimeout(() => {
                panel.classList.add('map-panel-open');
            }, 10);
            
            this.isOpen = true;
        }
    }

    closePanel() {
        const panel = document.getElementById('map-widget-panel');
        const toggleButton = document.getElementById('map-main-toggle');
        
        if (panel && toggleButton) {
            panel.classList.remove('map-panel-open');
            panel.setAttribute('aria-hidden', 'true');
            toggleButton.setAttribute('aria-expanded', 'false');
            
            // Hide after animation
            setTimeout(() => {
                panel.style.display = 'none';
            }, 300);
            
            this.isOpen = false;
        }
    }
}

// Initialize the demo when the page loads
new AccessibilityDemo();
