/* Category Grid Layout */
.map-category-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--map-space-3);
    margin-top: var(--map-space-2);
}

/* ===== KEYBOARD SHORTCUTS PREMIUM STYLING ===== */

/* Shortcuts Grid Layout */
.map-shortcuts-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--map-space-3);
    padding: var(--map-space-4);
    background: var(--map-gray-50);
    border-radius: var(--map-radius-lg);
    margin-top: var(--map-space-3);
}

/* Individual Shortcut Items */
.map-shortcut-item {
    display: flex;
    align-items: center;
    gap: var(--map-space-3);
    padding: var(--map-space-3);
    background: var(--map-white);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-md);
    transition: all var(--map-transition-base);
}

.map-shortcut-item:hover {
    border-color: var(--map-primary);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

/* Shortcut Key Styling */
.map-shortcut-key {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    padding: var(--map-space-2) var(--map-space-3);
    background: linear-gradient(135deg, var(--map-gray-100) 0%, var(--map-gray-200) 100%);
    border: 1px solid var(--map-gray-300);
    border-radius: var(--map-radius-md);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: var(--map-font-size-sm);
    font-weight: 600;
    color: var(--map-gray-700);
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.5);
    flex-shrink: 0;
}

/* Shortcut Description */
.map-shortcut-desc {
    flex: 1;
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-600);
    line-height: 1.5;
}

/* Expandable Content Animation - Enhanced for Proper Scrolling */
.map-expandable-content {
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Ensure expandable content doesn't get cut off */
.map-feature-section:last-child .map-expandable-content {
    margin-bottom: var(--map-space-6);
}

/* Responsive Design for Shortcuts */
@media (max-width: 480px) {
    .map-shortcut-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--map-space-2);
    }

    .map-shortcut-key {
        min-width: auto;
        align-self: flex-start;
    }
}

/* ===== ADHD FOCUS MODE ===== */
body.map-adhd-focus-mode {
    position: relative;
}

/* ADHD Focus Mode Overlay */
#map-adhd-focus-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2147483647 !important;
    pointer-events: none;
    display: none;
    transition: none;
    /* Ensure no filters affect the content */
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
}

/* Premium ADHD Focus Mode Border */
#map-adhd-focus-border {
    position: fixed;
    left: 0;
    width: 100%;
    border-top: 3px solid var(--map-primary);
    border-bottom: 3px solid var(--map-primary);
    border-left: none;
    border-right: none;
    z-index: 2147483648 !important;
    pointer-events: none;
    display: none;
    transition: none;

    /* Premium styling with enhanced ADHD focus glow */
    box-shadow:
        /* Outer glow effects - extending into dark overlay */
        0 -15px 30px rgba(99, 102, 241, 0.4),
        0 15px 30px rgba(99, 102, 241, 0.4),
        0 -25px 50px rgba(99, 102, 241, 0.25),
        0 25px 50px rgba(99, 102, 241, 0.25),
        0 -35px 70px rgba(99, 102, 241, 0.15),
        0 35px 70px rgba(99, 102, 241, 0.15),
        /* Subtle inner highlight */
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);

    /* 100% transparent background */
    background: transparent;
}

/* Enhanced border for high contrast mode */
@media (prefers-contrast: high) {
    #map-adhd-focus-border {
        border-top-width: 4px;
        border-bottom-width: 4px;
        border-top-color: #000000;
        border-bottom-color: #000000;
        box-shadow:
            0 0 0 2px #ffffff,
            0 0 0 6px #000000;
        background: transparent;
    }
}

/* Theme-aware border colors */
.map-theme-ocean #map-adhd-focus-border {
    border-top-color: #0ea5e9;
    border-bottom-color: #0ea5e9;
    box-shadow:
        /* Ocean theme ADHD focus glow */
        0 -15px 30px rgba(14, 165, 233, 0.4),
        0 15px 30px rgba(14, 165, 233, 0.4),
        0 -25px 50px rgba(14, 165, 233, 0.25),
        0 25px 50px rgba(14, 165, 233, 0.25),
        0 -35px 70px rgba(14, 165, 233, 0.15),
        0 35px 70px rgba(14, 165, 233, 0.15),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    background: transparent;
}

.map-theme-forest #map-adhd-focus-border {
    border-top-color: #059669;
    border-bottom-color: #059669;
    box-shadow:
        /* Forest theme ADHD focus glow */
        0 -15px 30px rgba(5, 150, 105, 0.4),
        0 15px 30px rgba(5, 150, 105, 0.4),
        0 -25px 50px rgba(5, 150, 105, 0.25),
        0 25px 50px rgba(5, 150, 105, 0.25),
        0 -35px 70px rgba(5, 150, 105, 0.15),
        0 35px 70px rgba(5, 150, 105, 0.15),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    background: transparent;
}

.map-theme-sunset #map-adhd-focus-border {
    border-top-color: #ea580c;
    border-bottom-color: #ea580c;
    box-shadow:
        /* Sunset theme ADHD focus glow */
        0 -15px 30px rgba(234, 88, 12, 0.4),
        0 15px 30px rgba(234, 88, 12, 0.4),
        0 -25px 50px rgba(234, 88, 12, 0.25),
        0 25px 50px rgba(234, 88, 12, 0.25),
        0 -35px 70px rgba(234, 88, 12, 0.15),
        0 35px 70px rgba(234, 88, 12, 0.15),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    background: transparent;
}

.map-theme-purple #map-adhd-focus-border {
    border-top-color: #7c3aed;
    border-bottom-color: #7c3aed;
    box-shadow:
        /* Purple theme ADHD focus glow */
        0 -15px 30px rgba(124, 58, 237, 0.4),
        0 15px 30px rgba(124, 58, 237, 0.4),
        0 -25px 50px rgba(124, 58, 237, 0.25),
        0 25px 50px rgba(124, 58, 237, 0.25),
        0 -35px 70px rgba(124, 58, 237, 0.15),
        0 35px 70px rgba(124, 58, 237, 0.15),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    background: transparent;
}

/* ===== DARK MODE ADHD FOCUS - PURPLE EFFECT ===== */

/* Ultra-specific selectors to override inline styles - Purple ADHD Focus ONLY in Dark Mode */
html body.map-dark-mode #map-adhd-focus-border,
html body:has(.map-accessibility-widget.map-dark-mode) #map-adhd-focus-border,
html .map-accessibility-widget.map-dark-mode ~ #map-adhd-focus-border {
    border-top: 3px solid #7c3aed !important;
    border-bottom: 3px solid #7c3aed !important;
    border-top-color: #7c3aed !important;
    border-bottom-color: #7c3aed !important;
}

/* Force purple glow with maximum specificity */
html body.map-dark-mode #map-adhd-focus-border,
html body:has(.map-accessibility-widget.map-dark-mode) #map-adhd-focus-border,
html .map-accessibility-widget.map-dark-mode ~ #map-adhd-focus-border {
    box-shadow:
        /* Purple ADHD focus glow - override all blue */
        0 -15px 30px rgba(124, 58, 237, 0.6) !important,
        0 15px 30px rgba(124, 58, 237, 0.6) !important,
        0 -25px 50px rgba(124, 58, 237, 0.4) !important,
        0 25px 50px rgba(124, 58, 237, 0.4) !important,
        0 -35px 70px rgba(124, 58, 237, 0.25) !important,
        0 35px 70px rgba(124, 58, 237, 0.25) !important,
        inset 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
}

/* Fallback with attribute selector to catch dynamically created elements */
#map-adhd-focus-border[style*="rgba(99, 102, 241"] {
    box-shadow:
        0 -15px 30px rgba(124, 58, 237, 0.6) !important,
        0 15px 30px rgba(124, 58, 237, 0.6) !important,
        0 -25px 50px rgba(124, 58, 237, 0.4) !important,
        0 25px 50px rgba(124, 58, 237, 0.4) !important,
        0 -35px 70px rgba(124, 58, 237, 0.25) !important,
        0 35px 70px rgba(124, 58, 237, 0.25) !important,
        inset 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
}

/* Ensure blue colors in light mode - default behavior */
html body:not(.map-dark-mode):not(:has(.map-accessibility-widget.map-dark-mode)) #map-adhd-focus-border {
    border-top: 3px solid #6366f1 !important;
    border-bottom: 3px solid #6366f1 !important;
    box-shadow:
        0 -15px 30px rgba(99, 102, 241, 0.6) !important,
        0 15px 30px rgba(99, 102, 241, 0.6) !important,
        0 -25px 50px rgba(99, 102, 241, 0.4) !important,
        0 25px 50px rgba(99, 102, 241, 0.4) !important,
        0 -35px 70px rgba(99, 102, 241, 0.25) !important,
        0 35px 70px rgba(99, 102, 241, 0.25) !important,
        inset 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
}

/* Force purple glow ONLY when dark mode widget exists */
body:has(.map-accessibility-widget.map-dark-mode) #map-adhd-focus-border,
.map-accessibility-widget.map-dark-mode ~ * #map-adhd-focus-border {
    border-top: 3px solid #7c3aed !important;
    border-bottom: 3px solid #7c3aed !important;
    box-shadow:
        0 -15px 30px rgba(124, 58, 237, 0.6) !important,
        0 15px 30px rgba(124, 58, 237, 0.6) !important,
        0 -25px 50px rgba(124, 58, 237, 0.4) !important,
        0 25px 50px rgba(124, 58, 237, 0.4) !important,
        0 -35px 70px rgba(124, 58, 237, 0.25) !important,
        0 35px 70px rgba(124, 58, 237, 0.25) !important,
        inset 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
}

/* Force accessibility widget to be below ADHD focus overlay */
body.map-adhd-focus-mode .map-accessibility-widget,
body.map-adhd-focus-mode .map-accessibility-widget *,
body.map-adhd-focus-mode .map-widget-panel,
body.map-adhd-focus-mode .map-main-toggle {
    z-index: 1000 !important;
}

/* ===== PREMIUM BIG CURSOR MODE ===== */

/* Default big cursor - Premium blue design */
body.map-big-cursor-mode,
body.map-big-cursor-mode * {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="60" height="48" viewBox="0 0 24 24"><path fill="%236366F1" stroke="%23FFFFFF" stroke-width="0.75" stroke-linejoin="round" d="M18 14.88 8.16 3.15c-.26-.31-.76-.12-.76.28v15.31c0 .36.42.56.7.33l3.1-2.6 1.55 4.25c.08.22.33.34.55.26l1.61-.59a.43.43 0 0 0 .26-.55l-1.55-4.25h4.05c.36 0 .56-.42.33-.7Z"/></svg>') 8 8, auto !important;
}

/* Big cursor for interactive elements - Premium blue pointer */
body.map-big-cursor-mode a,
body.map-big-cursor-mode button,
body.map-big-cursor-mode input[type="button"],
body.map-big-cursor-mode input[type="submit"],
body.map-big-cursor-mode select,
body.map-big-cursor-mode [role="button"],
body.map-big-cursor-mode .clickable {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><path fill="%236366F1" stroke="%23FFFFFF" stroke-width="0.75" stroke-linejoin="round" d="M10 11V8.99c0-.88.59-1.64 1.44-1.86h.05A1.99 1.99 0 0 1 14 9.05V12v-2c0-.88.6-1.65 1.46-1.87h.05A1.98 1.98 0 0 1 18 10.06V13v-1.94a2 2 0 0 1 1.51-1.94h0A2 2 0 0 1 22 11.06V14c0 .6-.08 1.27-.21 1.97a7.96 7.96 0 0 1-7.55 6.48 54.98 54.98 0 0 1-4.48 0 7.96 7.96 0 0 1-7.55-6.48C2.08 15.27 2 14.59 2 14v-1.49c0-1.11.9-2.01 2.01-2.01h0a2 2 0 0 1 2.01 2.03l-.01.97v-10c0-1.1.9-2 2-2h0a2 2 0 0 1 2 2V11Z"/></svg>') 12 12, pointer !important;
}

/* Big cursor for text input fields - Premium blue I-beam */
body.map-big-cursor-mode input[type="text"],
body.map-big-cursor-mode input[type="email"],
body.map-big-cursor-mode input[type="password"],
body.map-big-cursor-mode input[type="search"],
body.map-big-cursor-mode input[type="url"],
body.map-big-cursor-mode textarea,
body.map-big-cursor-mode [contenteditable] {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="32" viewBox="0 0 20 32"><rect x="8" y="2" width="4" height="28" fill="%236366f1" stroke="white" stroke-width="1"/><rect x="4" y="2" width="12" height="3" fill="%236366f1" stroke="white" stroke-width="1"/><rect x="4" y="27" width="12" height="3" fill="%236366f1" stroke="white" stroke-width="1"/></svg>') 10 16, text !important;
}

/* Big cursor for resize handles - Premium blue resize */
body.map-big-cursor-mode [resize],
body.map-big-cursor-mode .resize-handle {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="44" height="44" viewBox="0 0 32 32"><defs><filter id="glow"><feGaussianBlur stdDeviation="2" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><path d="M20 12l4 4-4 4M12 12l-4 4 4 4" stroke="%236366f1" stroke-width="3" fill="none" filter="url(%23glow)"/></svg>') 22 22, ew-resize !important;
}

/* ===== DARK MODE BIG CURSOR SUPPORT ===== */

/* Dark mode big cursor - Purple design */
body.map-dark-mode.map-big-cursor-mode,
body.map-dark-mode.map-big-cursor-mode *,
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode,
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode * {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="60" height="48" viewBox="0 0 24 24"><path fill="%237c3aed" stroke="%23FFFFFF" stroke-width="0.75" stroke-linejoin="round" d="M18 14.88 8.16 3.15c-.26-.31-.76-.12-.76.28v15.31c0 .36.42.56.7.33l3.1-2.6 1.55 4.25c.08.22.33.34.55.26l1.61-.59a.43.43 0 0 0 .26-.55l-1.55-4.25h4.05c.36 0 .56-.42.33-.7Z"/></svg>') 8 8, auto !important;
}

/* Dark mode big cursor for interactive elements - Purple pointer */
body.map-dark-mode.map-big-cursor-mode a,
body.map-dark-mode.map-big-cursor-mode button,
body.map-dark-mode.map-big-cursor-mode input[type="button"],
body.map-dark-mode.map-big-cursor-mode input[type="submit"],
body.map-dark-mode.map-big-cursor-mode select,
body.map-dark-mode.map-big-cursor-mode [role="button"],
body.map-dark-mode.map-big-cursor-mode .clickable,
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode a,
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode button,
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode input[type="button"],
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode input[type="submit"],
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode select,
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode [role="button"],
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode .clickable {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><path fill="%237c3aed" stroke="%23FFFFFF" stroke-width="0.75" stroke-linejoin="round" d="M10 11V8.99c0-.88.59-1.64 1.44-1.86h.05A1.99 1.99 0 0 1 14 9.05V12v-2c0-.88.6-1.65 1.46-1.87h.05A1.98 1.98 0 0 1 18 10.06V13v-1.94a2 2 0 0 1 1.51-1.94h0A2 2 0 0 1 22 11.06V14c0 .6-.08 1.27-.21 1.97a7.96 7.96 0 0 1-7.55 6.48 54.98 54.98 0 0 1-4.48 0 7.96 7.96 0 0 1-7.55-6.48C2.08 15.27 2 14.59 2 14v-1.49c0-1.11.9-2.01 2.01-2.01h0a2 2 0 0 1 2.01 2.03l-.01.97v-10c0-1.1.9-2 2-2h0a2 2 0 0 1 2 2V11Z"/></svg>') 12 12, pointer !important;
}

/* Dark mode big cursor for text input fields - Purple I-beam */
body.map-dark-mode.map-big-cursor-mode input[type="text"],
body.map-dark-mode.map-big-cursor-mode input[type="email"],
body.map-dark-mode.map-big-cursor-mode input[type="password"],
body.map-dark-mode.map-big-cursor-mode input[type="search"],
body.map-dark-mode.map-big-cursor-mode input[type="url"],
body.map-dark-mode.map-big-cursor-mode textarea,
body.map-dark-mode.map-big-cursor-mode [contenteditable],
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode input[type="text"],
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode input[type="email"],
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode input[type="password"],
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode input[type="search"],
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode input[type="url"],
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode textarea,
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode [contenteditable] {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="32" viewBox="0 0 20 32"><rect x="8" y="2" width="4" height="28" fill="%237c3aed" stroke="white" stroke-width="1"/><rect x="4" y="2" width="12" height="3" fill="%237c3aed" stroke="white" stroke-width="1"/><rect x="4" y="27" width="12" height="3" fill="%237c3aed" stroke="white" stroke-width="1"/></svg>') 10 16, text !important;
}

/* Dark mode big cursor for resize handles - Purple resize */
body.map-dark-mode.map-big-cursor-mode [resize],
body.map-dark-mode.map-big-cursor-mode .resize-handle,
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode [resize],
body:has(.map-accessibility-widget.map-dark-mode).map-big-cursor-mode .resize-handle {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="44" height="44" viewBox="0 0 32 32"><defs><filter id="glow"><feGaussianBlur stdDeviation="2" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><path d="M20 12l4 4-4 4M12 12l-4 4 4 4" stroke="%237c3aed" stroke-width="3" fill="none" filter="url(%23glow)"/></svg>') 22 22, ew-resize !important;
}

/* ===== THEME-AWARE BIG CURSORS ===== */

/* Ocean Theme - Sky Blue Cursors */
.map-theme-ocean body.map-big-cursor-mode,
.map-theme-ocean body.map-big-cursor-mode * {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 32 32"><defs><filter id="glow"><feGaussianBlur stdDeviation="2" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><path d="M4 4v24l8-8h8l-8-8V4z" fill="%230ea5e9" stroke="white" stroke-width="2" filter="url(%23glow)"/></svg>') 6 6, auto !important;
}

.map-theme-ocean body.map-big-cursor-mode a,
.map-theme-ocean body.map-big-cursor-mode button,
.map-theme-ocean body.map-big-cursor-mode input[type="button"],
.map-theme-ocean body.map-big-cursor-mode input[type="submit"],
.map-theme-ocean body.map-big-cursor-mode select,
.map-theme-ocean body.map-big-cursor-mode [role="button"],
.map-theme-ocean body.map-big-cursor-mode .clickable {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="44" height="44" viewBox="0 0 32 32"><defs><filter id="glow"><feGaussianBlur stdDeviation="2" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><path d="M4 4v24l8-8h8l-8-8V4z" fill="%230ea5e9" stroke="white" stroke-width="2" filter="url(%23glow)"/><circle cx="24" cy="8" r="3" fill="%230ea5e9" opacity="0.8"/></svg>') 6 6, pointer !important;
}

.map-theme-ocean body.map-big-cursor-mode input[type="text"],
.map-theme-ocean body.map-big-cursor-mode input[type="email"],
.map-theme-ocean body.map-big-cursor-mode input[type="password"],
.map-theme-ocean body.map-big-cursor-mode input[type="search"],
.map-theme-ocean body.map-big-cursor-mode input[type="url"],
.map-theme-ocean body.map-big-cursor-mode textarea,
.map-theme-ocean body.map-big-cursor-mode [contenteditable] {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 32 32"><defs><filter id="glow"><feGaussianBlur stdDeviation="1.5" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><path d="M16 4v24M12 8h8M12 24h8" stroke="%230ea5e9" stroke-width="3" fill="none" filter="url(%23glow)"/></svg>') 20 20, text !important;
}

/* Forest Theme - Green Cursors */
.map-theme-forest body.map-big-cursor-mode,
.map-theme-forest body.map-big-cursor-mode * {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 32 32"><defs><filter id="glow"><feGaussianBlur stdDeviation="2" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><path d="M4 4v24l8-8h8l-8-8V4z" fill="%23059669" stroke="white" stroke-width="2" filter="url(%23glow)"/></svg>') 6 6, auto !important;
}

.map-theme-forest body.map-big-cursor-mode a,
.map-theme-forest body.map-big-cursor-mode button,
.map-theme-forest body.map-big-cursor-mode input[type="button"],
.map-theme-forest body.map-big-cursor-mode input[type="submit"],
.map-theme-forest body.map-big-cursor-mode select,
.map-theme-forest body.map-big-cursor-mode [role="button"],
.map-theme-forest body.map-big-cursor-mode .clickable {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="44" height="44" viewBox="0 0 32 32"><defs><filter id="glow"><feGaussianBlur stdDeviation="2" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><path d="M4 4v24l8-8h8l-8-8V4z" fill="%23059669" stroke="white" stroke-width="2" filter="url(%23glow)"/><circle cx="24" cy="8" r="3" fill="%23059669" opacity="0.8"/></svg>') 6 6, pointer !important;
}

.map-theme-forest body.map-big-cursor-mode input[type="text"],
.map-theme-forest body.map-big-cursor-mode input[type="email"],
.map-theme-forest body.map-big-cursor-mode input[type="password"],
.map-theme-forest body.map-big-cursor-mode input[type="search"],
.map-theme-forest body.map-big-cursor-mode input[type="url"],
.map-theme-forest body.map-big-cursor-mode textarea,
.map-theme-forest body.map-big-cursor-mode [contenteditable] {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 32 32"><defs><filter id="glow"><feGaussianBlur stdDeviation="1.5" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><path d="M16 4v24M12 8h8M12 24h8" stroke="%23059669" stroke-width="3" fill="none" filter="url(%23glow)"/></svg>') 20 20, text !important;
}

/* Sunset Theme - Orange Cursors */
.map-theme-sunset body.map-big-cursor-mode,
.map-theme-sunset body.map-big-cursor-mode * {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 32 32"><defs><filter id="glow"><feGaussianBlur stdDeviation="2" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><path d="M4 4v24l8-8h8l-8-8V4z" fill="%23ea580c" stroke="white" stroke-width="2" filter="url(%23glow)"/></svg>') 6 6, auto !important;
}

.map-theme-sunset body.map-big-cursor-mode a,
.map-theme-sunset body.map-big-cursor-mode button,
.map-theme-sunset body.map-big-cursor-mode input[type="button"],
.map-theme-sunset body.map-big-cursor-mode input[type="submit"],
.map-theme-sunset body.map-big-cursor-mode select,
.map-theme-sunset body.map-big-cursor-mode [role="button"],
.map-theme-sunset body.map-big-cursor-mode .clickable {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="44" height="44" viewBox="0 0 32 32"><defs><filter id="glow"><feGaussianBlur stdDeviation="2" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><path d="M4 4v24l8-8h8l-8-8V4z" fill="%23ea580c" stroke="white" stroke-width="2" filter="url(%23glow)"/><circle cx="24" cy="8" r="3" fill="%23ea580c" opacity="0.8"/></svg>') 6 6, pointer !important;
}

.map-theme-sunset body.map-big-cursor-mode input[type="text"],
.map-theme-sunset body.map-big-cursor-mode input[type="email"],
.map-theme-sunset body.map-big-cursor-mode input[type="password"],
.map-theme-sunset body.map-big-cursor-mode input[type="search"],
.map-theme-sunset body.map-big-cursor-mode input[type="url"],
.map-theme-sunset body.map-big-cursor-mode textarea,
.map-theme-sunset body.map-big-cursor-mode [contenteditable] {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 32 32"><defs><filter id="glow"><feGaussianBlur stdDeviation="1.5" result="coloredBlur"/><feMerge><feMergeNode in="coloredBlur"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><path d="M16 4v24M12 8h8M12 24h8" stroke="%23ea580c" stroke-width="3" fill="none" filter="url(%23glow)"/></svg>') 20 20, text !important;
}

/* Purple Theme - Purple Cursors */
.map-theme-purple body.map-big-cursor-mode,
.map-theme-purple body.map-big-cursor-mode * {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="60" height="48" viewBox="0 0 24 24"><path fill="%237c3aed" d="M19.27 15.32 7.77 1.61A1 1 0 0 0 6 2.25v17.89a1 1 0 0 0 1.64.77l3.08-2.58 1.56 4.3a1 1 0 0 0 1.28.6l1.88-.68a1 1 0 0 0 .6-1.28l-1.56-4.3h4.02a1 1 0 0 0 .77-1.64Z"></path></svg>') 6 6, auto !important;
}

.map-theme-purple body.map-big-cursor-mode a,
.map-theme-purple body.map-big-cursor-mode button,
.map-theme-purple body.map-big-cursor-mode input[type="button"],
.map-theme-purple body.map-big-cursor-mode input[type="submit"],
.map-theme-purple body.map-big-cursor-mode select,
.map-theme-purple body.map-big-cursor-mode [role="button"],
.map-theme-purple body.map-big-cursor-mode .clickable {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="60" height="48" viewBox="0 0 24 24"><path fill="%237c3aed" d="M19.27 15.32 7.77 1.61A1 1 0 0 0 6 2.25v17.89a1 1 0 0 0 1.64.77l3.08-2.58 1.56 4.3a1 1 0 0 0 1.28.6l1.88-.68a1 1 0 0 0 .6-1.28l-1.56-4.3h4.02a1 1 0 0 0 .77-1.64Z"></path></svg>') 6 6, pointer !important;
}

.map-theme-purple body.map-big-cursor-mode input[type="text"],
.map-theme-purple body.map-big-cursor-mode input[type="email"],
.map-theme-purple body.map-big-cursor-mode input[type="password"],
.map-theme-purple body.map-big-cursor-mode input[type="search"],
.map-theme-purple body.map-big-cursor-mode input[type="url"],
.map-theme-purple body.map-big-cursor-mode textarea,
.map-theme-purple body.map-big-cursor-mode [contenteditable] {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="60" height="48" viewBox="0 0 24 24"><path fill="%237c3aed" d="M19.27 15.32 7.77 1.61A1 1 0 0 0 6 2.25v17.89a1 1 0 0 0 1.64.77l3.08-2.58 1.56 4.3a1 1 0 0 0 1.28.6l1.88-.68a1 1 0 0 0 .6-1.28l-1.56-4.3h4.02a1 1 0 0 0 .77-1.64Z"></path></svg>') 6 6, text !important;
}

/* ===== TEXT MAGNIFICATION MODE ===== */

/* Text Magnification Window */
.map-magnification-window {
    position: absolute;
    z-index: 999999;
    background: var(--map-white);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-lg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    padding: var(--map-space-4);
    max-width: 300px;
    min-width: 150px;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    pointer-events: none;
    user-select: none;
    transition: all var(--map-transition-base);
    opacity: 0;
    transform: scale(0.9);
}

/* Magnification Window Content */
.map-magnification-content {
    font-size: 1.8em;
    line-height: 1.4;
    color: var(--map-gray-800);
    font-weight: 500;
    word-wrap: break-word;
    text-align: left;
}

/* Show magnification window with animation */
.map-magnification-window:not([style*="display: none"]) {
    opacity: 1;
    transform: scale(1);
}

/* ===== THEME-AWARE MAGNIFICATION WINDOWS ===== */

/* Dark theme support for magnification window */
body.map-theme-dark .map-magnification-window {
    background: var(--map-gray-800);
    border-color: var(--map-gray-600);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

body.map-theme-dark .map-magnification-content {
    color: var(--map-white);
}

/* High contrast theme support */
body.map-theme-high-contrast .map-magnification-window {
    background: #000000;
    border: 2px solid #ffffff;
    box-shadow: 0 8px 32px rgba(255, 255, 255, 0.2);
}

body.map-theme-high-contrast .map-magnification-content {
    color: #ffffff;
    font-weight: 700;
}

/* Monochrome theme support */
body.map-theme-monochrome .map-magnification-window {
    background: #f5f5f5;
    border-color: #333333;
}

body.map-theme-monochrome .map-magnification-content {
    color: #000000;
}

/* Colorblind theme support */
body.map-theme-colorblind .map-magnification-window {
    background: #f0f0f0;
    border: 2px solid #000000;
}

body.map-theme-colorblind .map-magnification-content {
    color: #000000;
    font-weight: 600;
}

/* ===== RESPONSIVE DESIGN FOR MAGNIFICATION ===== */

@media (max-width: 768px) {
    .map-magnification-window {
        max-width: 250px;
        padding: var(--map-space-3);
        font-size: 0.9em;
    }

    .map-magnification-content {
        font-size: 1.6em;
        line-height: 1.3;
    }
}

@media (max-width: 480px) {
    .map-magnification-window {
        max-width: 200px;
        padding: var(--map-space-2);
    }

    .map-magnification-content {
        font-size: 1.4em;
        line-height: 1.2;
    }
}
