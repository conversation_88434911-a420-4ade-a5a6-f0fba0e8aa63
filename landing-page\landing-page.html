<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Accessibility Plugin - Premium WordPress Accessibility Solution</title>
    <meta name="description" content="Transform your WordPress website with our premium accessibility plugin. Features text-to-speech, contrast themes, dyslexic fonts, and more.">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom Styles -->
    <link rel="stylesheet" href="landing-page.css">

    <!-- Accessibility Plugin CSS Files -->
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/widget-core.css">
    <link rel="stylesheet" href="assets/css/ui-components.css">
    <link rel="stylesheet" href="assets/css/modal-system.css">
    <link rel="stylesheet" href="assets/css/category-menu.css">
    <link rel="stylesheet" href="assets/css/text-features.css">
    <link rel="stylesheet" href="assets/css/color-features.css">
    <link rel="stylesheet" href="assets/css/navigation-features.css">
    <link rel="stylesheet" href="assets/css/preferences.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link rel="stylesheet" href="assets/css/utilities.css">

    <!-- Custom styles for widget positioning -->
    <style id="map-widget-custom-styles">
        :root {
            --map-primary-color: #0073aa;
            --map-primary-hover: #005a87;
            --map-primary-active: #004a6b;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <a href="#" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-universal-access"></i>
                </div>
                My Accessibility Plugin
            </a>
            
            <ul class="nav-links">
                <li><a href="#features">Features</a></li>
                <li><a href="#demo">Live Demo</a></li>
                <li><a href="#pricing">Pricing</a></li>
                <li><a href="#support">Support</a></li>
            </ul>
            
            <a href="#" class="cta-button">
                <i class="fas fa-download"></i>
                Download Now
            </a>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    Make Your WordPress Site Accessible to Everyone
                </h1>
                <p class="hero-subtitle">
                    Transform your website with our premium accessibility plugin featuring text-to-speech, 
                    contrast themes, dyslexic fonts, and comprehensive accessibility tools.
                </p>
                <div class="hero-buttons">
                    <a href="#demo" class="cta-button">
                        <i class="fas fa-play"></i>
                        Try Live Demo
                    </a>
                    <a href="#features" class="btn-secondary">
                        <i class="fas fa-list"></i>
                        View Features
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <h2 class="section-title">Powerful Accessibility Features</h2>
            <p class="section-subtitle">
                Everything you need to make your WordPress website accessible and inclusive for all users.
            </p>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-volume-up"></i>
                    </div>
                    <h3 class="feature-title">Text-to-Speech</h3>
                    <p class="feature-description">
                        Advanced text-to-speech functionality with customizable voice settings, 
                        reading speed control, and multi-language support for enhanced accessibility.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3 class="feature-title">Contrast Themes</h3>
                    <p class="feature-description">
                        Multiple contrast themes including dark mode, high contrast, and colorblind-friendly 
                        options to improve readability for all users.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-font"></i>
                    </div>
                    <h3 class="feature-title">Dyslexic Font Support</h3>
                    <p class="feature-description">
                        OpenDyslexic font integration with customizable font sizes and spacing 
                        to help users with dyslexia read more comfortably.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mouse-pointer"></i>
                    </div>
                    <h3 class="feature-title">Navigation Assistance</h3>
                    <p class="feature-description">
                        Enhanced navigation tools including focus indicators, keyboard navigation, 
                        and ADHD-friendly reading guides for better user experience.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h3 class="feature-title">Customizable Settings</h3>
                    <p class="feature-description">
                        Comprehensive admin panel with easy-to-use settings, user preferences 
                        storage, and seamless WordPress integration.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">Fully Responsive</h3>
                    <p class="feature-description">
                        Mobile-first design that works perfectly on all devices and screen sizes, 
                        ensuring accessibility features are available everywhere.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Section Placeholder -->
    <section id="demo" class="demo-section">
        <div class="container">
            <h2 class="section-title">Live Demo</h2>
            <p class="section-subtitle">
                Experience the plugin in action with our interactive demo.
            </p>

            <div class="demo-placeholder">
                <h3><i class="fas fa-play-circle"></i> Interactive Demo Coming Soon</h3>
                <p>This section will contain the live demo of the accessibility plugin.</p>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing">
        <div class="container">
            <h2 class="section-title">Simple, Transparent Pricing</h2>
            <p class="section-subtitle">
                Choose the perfect plan for your WordPress website needs.
            </p>

            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3 class="pricing-title">Single Site</h3>
                        <div class="pricing-price">
                            <span class="currency">$</span>
                            <span class="amount">49</span>
                        </div>
                        <p class="pricing-period">One-time payment</p>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> 1 WordPress Site</li>
                        <li><i class="fas fa-check"></i> All Premium Features</li>
                        <li><i class="fas fa-check"></i> 6 Months Support</li>
                        <li><i class="fas fa-check"></i> Regular Updates</li>
                        <li><i class="fas fa-check"></i> Documentation</li>
                    </ul>
                    <a href="#" class="pricing-button">Get Started</a>
                </div>

                <div class="pricing-card featured">
                    <div class="pricing-badge">Most Popular</div>
                    <div class="pricing-header">
                        <h3 class="pricing-title">Developer</h3>
                        <div class="pricing-price">
                            <span class="currency">$</span>
                            <span class="amount">99</span>
                        </div>
                        <p class="pricing-period">One-time payment</p>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> Unlimited Sites</li>
                        <li><i class="fas fa-check"></i> All Premium Features</li>
                        <li><i class="fas fa-check"></i> 12 Months Support</li>
                        <li><i class="fas fa-check"></i> Priority Updates</li>
                        <li><i class="fas fa-check"></i> Advanced Documentation</li>
                        <li><i class="fas fa-check"></i> White Label Rights</li>
                    </ul>
                    <a href="#" class="pricing-button">Get Started</a>
                </div>

                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3 class="pricing-title">Extended</h3>
                        <div class="pricing-price">
                            <span class="currency">$</span>
                            <span class="amount">199</span>
                        </div>
                        <p class="pricing-period">One-time payment</p>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> Unlimited Sites</li>
                        <li><i class="fas fa-check"></i> All Premium Features</li>
                        <li><i class="fas fa-check"></i> Lifetime Support</li>
                        <li><i class="fas fa-check"></i> Priority Updates</li>
                        <li><i class="fas fa-check"></i> Source Code Access</li>
                        <li><i class="fas fa-check"></i> Custom Development</li>
                    </ul>
                    <a href="#" class="pricing-button">Get Started</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <div class="logo-icon">
                            <i class="fas fa-universal-access"></i>
                        </div>
                        <span>My Accessibility Plugin</span>
                    </div>
                    <p class="footer-description">
                        Making the web accessible for everyone with premium WordPress accessibility solutions.
                    </p>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">Product</h4>
                    <ul class="footer-links">
                        <li><a href="#features">Features</a></li>
                        <li><a href="#demo">Live Demo</a></li>
                        <li><a href="#pricing">Pricing</a></li>
                        <li><a href="#">Documentation</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">Support</h4>
                    <ul class="footer-links">
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">Bug Reports</a></li>
                        <li><a href="#">Feature Requests</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">Legal</h4>
                    <ul class="footer-links">
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">License</a></li>
                        <li><a href="#">Refund Policy</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 My Accessibility Plugin. All rights reserved.</p>
                <div class="footer-social">
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                    <a href="#"><i class="fab fa-github"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Accessibility Widget -->
    <div id="map-accessibility-widget" class="map-accessibility-widget map-position-bottom-right map-style-modern map-size-medium" role="region" aria-label="Accessibility Tools">

        <!-- Main Toggle Button -->
        <button id="map-main-toggle" class="map-main-toggle" type="button" aria-expanded="false" aria-controls="map-widget-panel" aria-label="Open accessibility tools">
            <span class="map-toggle-icon" aria-hidden="true">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2M12 8C13.1 8 14 8.9 14 10C14 11.1 13.1 12 12 12C10.9 12 10 11.1 10 10C10 8.9 10.9 8 12 8M12 14C13.1 14 14 14.9 14 16C14 17.1 13.1 18 12 18C10.9 18 10 17.1 10 16C10 14.9 10.9 14 12 14M6 8C7.1 8 8 8.9 8 10C8 11.1 7.1 12 6 12C4.9 12 4 11.1 4 10C4 8.9 4.9 8 6 8M18 8C19.1 8 20 8.9 20 10C20 11.1 19.1 12 18 12C16.9 12 16 11.1 16 10C16 8.9 16.9 8 18 8M6 14C7.1 14 8 14.9 8 16C8 17.1 7.1 18 6 18C4.9 18 4 17.1 4 16C4 14.9 4.9 14 6 14M18 14C19.1 14 20 14.9 20 16C20 17.1 19.1 18 18 18C16.9 18 16 17.1 16 16C16 14.9 16.9 14 18 14"/>
                </svg>
            </span>
        </button>

        <!-- Widget Panel -->
        <div id="map-widget-panel" class="map-widget-panel" style="display: none;" aria-hidden="true">
            <div class="map-panel-header">
                <!-- Main title - shown when on main menu -->
                <h3 id="map-panel-title" class="map-panel-title">Accessibility Tools</h3>

                <!-- Back button with category title - shown when in category views -->
                <div id="map-header-navigation" class="map-header-navigation" style="display: none;">
                    <button id="map-header-back-button" class="map-header-back-button" type="button" aria-label="Back to main menu">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/>
                        </svg>
                    </button>
                    <h4 id="map-header-category-title" class="map-header-category-title"></h4>
                </div>

                <div class="map-header-buttons">
                    <button id="map-reset-category" class="map-reset-button" type="button" aria-label="Reset category options to default" style="display: none;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                        </svg>
                        <span>Reset</span>
                    </button>
                    <button id="map-close-panel" class="map-close-button" type="button" aria-label="Close accessibility tools">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="map-panel-content">
                <!-- Premium Animation Container for Modal Views -->
                <div class="map-modal-views-container">
                    <!-- Main Menu (Level 1) - Category Selection -->
                <div id="map-main-menu" class="map-modal-view map-modal-view-active" role="menu" aria-label="Accessibility categories">
                    <div class="map-category-grid">
                        <!-- Text Category -->
                        <button id="map-category-text" class="map-category-button" type="button" role="menuitem" data-category="text" aria-describedby="map-category-text-desc">
                            <div class="map-category-icon map-category-icon-text">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                    <polyline points="10,9 9,9 8,9"/>
                                </svg>
                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Text</div>
                                <div id="map-category-text-desc" class="map-category-desc">Reading and text options</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>

                        <!-- Colors Category -->
                        <button id="map-category-colors" class="map-category-button" type="button" role="menuitem" data-category="colors" aria-describedby="map-category-colors-desc">
                            <div class="map-category-icon map-category-icon-colors">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="13.5" cy="6.5" r=".5"/>
                                    <circle cx="17.5" cy="10.5" r=".5"/>
                                    <circle cx="8.5" cy="7.5" r=".5"/>
                                    <circle cx="6.5" cy="12.5" r=".5"/>
                                    <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"/>
                                </svg>
                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Contrast & Colors</div>
                                <div id="map-category-colors-desc" class="map-category-desc">Contrast and color themes</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>
                        <!-- Navigation Category -->
                        <button id="map-category-navigation" class="map-category-button" type="button" role="menuitem" data-category="navigation" aria-describedby="map-category-navigation-desc">
                            <div class="map-category-icon map-category-icon-navigation">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect x="2" y="4" width="20" height="16" rx="2"/>
                                    <path d="M6 8h.01"/>
                                    <path d="M10 8h.01"/>
                                    <path d="M14 8h.01"/>
                                    <path d="M18 8h.01"/>
                                    <path d="M8 12h.01"/>
                                    <path d="M12 12h.01"/>
                                    <path d="M16 12h.01"/>
                                    <path d="M7 16h10"/>
                                </svg>
                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Navigation</div>
                                <div id="map-category-navigation-desc" class="map-category-desc">Keyboard shortcuts and navigation aids</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>
                        <!-- Preferences Category -->
                        <button id="map-category-preferences" class="map-category-button" type="button" role="menuitem" data-category="preferences" aria-describedby="map-category-preferences-desc">
                            <div class="map-category-icon map-category-icon-preferences">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="3"/>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                </svg>
                            </div>
                            <div class="map-category-content">
                                <div class="map-category-title">Preferences</div>
                                <div id="map-category-preferences-desc" class="map-category-desc">General settings and preferences</div>
                            </div>
                            <div class="map-category-arrow" aria-hidden="true">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                </svg>
                            </div>
                        </button>
                    </div>
                </div>

                <!-- Text Category View (Level 2) -->
                <div id="map-view-text" class="map-modal-view" role="region" aria-label="Text accessibility options" data-title="Text Options">
                    <div class="map-view-content">
                        <!-- Text-to-Speech Section -->
                        <div class="map-feature-section">
                            <button id="map-tts-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Text to Speech</div>
                                    <div class="map-feature-desc">Select text to hear it read aloud</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>
                        </div>

                        <!-- Dyslexic Font Toggle Section -->
                        <div class="map-feature-section">
                            <button id="map-dyslexic-font-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M4 3h8c5.5 0 10 4.5 10 10s-4.5 10-10 10H4V3zm3 3v14h5c3.9 0 7-3.1 7-7s-3.1-7-7-7H7z"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Enable Dyslexic Font</div>
                                    <div class="map-feature-desc">Apply dyslexia-friendly font to improve readability</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>
                        </div>

                        <!-- Reading Guide Section -->
                        <div class="map-feature-section">
                            <button id="map-reading-guide-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <g transform="rotate(30 12 12)">
                                            <rect x="2" y="8" width="20" height="8" fill="none" stroke="currentColor" stroke-width="2"/>
                                            <line x1="4" y1="16" x2="4" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                            <line x1="5.5" y1="16" x2="5.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                            <line x1="7" y1="16" x2="7" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                            <line x1="8.5" y1="16" x2="8.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                            <line x1="10" y1="16" x2="10" y2="13.5" stroke="currentColor" stroke-width="2"/>
                                            <line x1="11.5" y1="16" x2="11.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                            <line x1="13" y1="16" x2="13" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                            <line x1="14.5" y1="16" x2="14.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                            <line x1="16" y1="16" x2="16" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                            <line x1="17.5" y1="16" x2="17.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                            <line x1="19" y1="16" x2="19" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                            <line x1="20.5" y1="16" x2="20.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                            <circle cx="18" cy="10.5" r="1.5" fill="none" stroke="currentColor" stroke-width="1.5"/>
                                            <circle cx="18" cy="10.5" r="0.5" fill="currentColor"/>
                                        </g>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Reading Guide</div>
                                    <div class="map-feature-desc">Show a horizontal line that follows your mouse to help focus on text</div>
                                </div>
                                <div class="map-toggle-switch">
                                    <span class="map-toggle-slider"></span>
                                </div>
                            </button>
                        </div>

                        <!-- Font Size Control Section -->
                        <div class="map-feature-section">
                            <button id="map-font-size-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8 2h14v4h-5v16h-4V6H8V2z"/>
                                        <path d="M2 12h8v2.5H7v7.5H5v-7.5H2V12z"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Font Size</div>
                                    <div class="map-feature-desc">Adjust text size for better readability</div>
                                </div>
                                <div class="map-feature-status">
                                    <span id="map-font-size-value">Default</span>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-font-size-controls" class="map-feature-controls" style="display: none;">
                                <div class="map-font-size-controls">
                                    <div class="map-size-control-group">
                                        <button id="map-font-size-decrease" class="map-size-control-btn map-size-decrease" type="button" aria-label="Decrease font size">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round">
                                                <path d="M5 12h14"/>
                                            </svg>
                                        </button>

                                        <div class="map-size-indicator">
                                            <div class="map-size-preview">Aa</div>
                                            <div class="map-size-percentage" id="map-font-percentage">100%</div>
                                        </div>

                                        <button id="map-font-size-increase" class="map-size-control-btn map-size-increase" type="button" aria-label="Increase font size">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round">
                                                <path d="M12 5v14M5 12h14"/>
                                            </svg>
                                        </button>
                                    </div>

                                    <button id="map-font-size-reset" class="map-control-reset" type="button" aria-label="Reset font size to default">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                            <path d="M21 3v5h-5"/>
                                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                            <path d="M3 21v-5h5"/>
                                        </svg>
                                        Reset to Default
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Line Spacing Control Section -->
                        <div class="map-feature-section">
                            <button id="map-line-spacing-toggle" class="map-feature-toggle" data-active="false" type="button">
                                <div class="map-feature-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M6 7h12v2H6V7zm0 4h12v2H6v-2zm0 4h12v2H6v-2z"/>
                                        <path d="M3 4h2v16H3V4z"/>
                                        <path d="M19 4h2v16h-2V4z"/>
                                        <circle cx="4" cy="6" r="1" fill="currentColor"/>
                                        <circle cx="4" cy="12" r="1" fill="currentColor"/>
                                        <circle cx="4" cy="18" r="1" fill="currentColor"/>
                                        <circle cx="20" cy="6" r="1" fill="currentColor"/>
                                        <circle cx="20" cy="12" r="1" fill="currentColor"/>
                                        <circle cx="20" cy="18" r="1" fill="currentColor"/>
                                    </svg>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-feature-title">Line Spacing</div>
                                    <div class="map-feature-desc">Adjust space between lines for better readability</div>
                                </div>
                                <div class="map-feature-status">
                                    <span id="map-line-spacing-value">Default</span>
                                </div>
                                <div class="map-category-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 18l6-6-6-6"/>
                                    </svg>
                                </div>
                            </button>

                            <div id="map-line-spacing-controls" class="map-feature-controls" style="display: none;">
                                <div class="map-line-spacing-controls">
                                    <div class="map-spacing-control-group">
                                        <div class="map-spacing-labels">
                                            <span class="map-spacing-label-min">Tight</span>
                                            <span class="map-spacing-label-center">Normal</span>
                                            <span class="map-spacing-label-max">Wide</span>
                                        </div>

                                        <div class="map-slider-container">
                                            <input type="range"
                                                   id="map-line-spacing-slider"
                                                   class="map-premium-slider"
                                                   min="1.0"
                                                   max="2.5"
                                                   step="0.1"
                                                   value="1.5"
                                                   aria-label="Adjust line spacing">
                                            <div class="map-slider-track">
                                                <div class="map-slider-progress" id="map-slider-progress"></div>
                                            </div>
                                        </div>

                                        <div class="map-spacing-value">
                                            <span id="map-spacing-numeric">1.5x</span>
                                        </div>
                                    </div>

                                    <button id="map-line-spacing-reset" class="map-control-reset" type="button" aria-label="Reset line spacing to default">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                            <path d="M21 3v5h-5"/>
                                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                            <path d="M3 21v-5h5"/>
                                        </svg>
                                        Reset to Default
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                </div> <!-- Close map-modal-views-container -->
            </div>
        </div>
    </div>

    <!-- Custom JavaScript -->
    <script src="landing-page.js"></script>
    <!-- Accessibility Plugin JavaScript -->
    <script src="assets/js/frontend.js"></script>
</body>
</html>
